import { type RouteConfig, index, route, layout, prefix } from "@react-router/dev/routes";

export default [
  // Public routes
  layout("./components/layout/PublicLayout.tsx", [
    index("./routes/_index.tsx"),
    route("login", "./routes/login.tsx"),
    route("register", "./routes/register.tsx"),
    route("forgot-password", "./routes/forgot-password.tsx")
  ]),

  // Client area routes
  ...prefix("client", [
    layout("./components/layout/ClientLayout.tsx", [
      index("./routes/client/_index.tsx"),
      route("services", "./routes/client/services.tsx"),
      route("appointments", "./routes/client/appointments.tsx"),
      route("profile", "./routes/client/profile.tsx"),
    ]),
  ]),

  // Business area routes
  ...prefix("business", [
    layout("./components/layout/BusinessLayout.tsx", [
      index("./routes/business/_index.tsx"),
      route("services", "./routes/business/services.tsx"),
      route("appointments", "./routes/business/appointments.tsx"),
      route("profile", "./routes/business/profile.tsx"),
    ]),
  ]),
] satisfies RouteConfig;




