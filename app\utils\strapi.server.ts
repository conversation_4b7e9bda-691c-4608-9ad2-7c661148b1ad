import { strapi } from '@strapi/client';

// Types for Strapi responses
interface StrapiUser {
  id: string;
  username: string;
  email: string;
  accountType: 'client' | 'business';
}

interface StrapiAuthResponse {
  jwt: string;
  user: StrapiUser;
}

interface BusinessData {
  id: string;
  name: string;
  owner: string;
  completed_appointments: number;
  completed_appointments_percentage: string;
  pending_bookings: number;
  pending_bookings_percentage: string;
}

// Create a Strapi client instance
const client = strapi({
    baseURL: 'http://strapi.agenda-servicii.ro/api',
    // Auth configuration
    auth: 'c96ee144d544795b07d85c96ab1ed39e9beaf64ea9f7e705475b3a41da646e282b1e06eae4eee0510122070110bf52534df631c85b585d961d7a95de9c6d2d9288d8877685351efc64b5b4edca90f97b9a5306d8551dcc4a6613e0d04cb7ba508b8cfa5011a65485e61e664e0ba71eaf7b888234b4fab61be04e56678ba1649a',
});

// Login user with email and password
export async function loginUser(email: string, password: string): Promise<StrapiAuthResponse> {
  try {
    const response = await client.fetch('/auth/local', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: email,
        password: password,
      }),
    });

    if (!response.ok) {
      throw new Error('Invalid credentials');
    }

    const data = await response.json();

    return {
      jwt: data.jwt,
      user: {
        id: data.user.id.toString(),
        username: data.user.username,
        email: data.user.email,
        accountType: data.user.accountType,
      }
    };
  } catch (error) {
    console.error('Strapi login error:', error);
    throw new Error('Invalid credentials');
  }
}
// Register user
export async function registerUser(username: string, email: string, password: string): Promise<StrapiAuthResponse> {
  try {
    const response = await client.fetch('/auth/local/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username,
        email,
        password,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error.message || 'Registration failed');
    }

    const data = await response.json();

    return {
      jwt: data.jwt,
      user: {
        id: data.user.id.toString(),
        username: data.user.username,
        email: data.user.email,
        accountType: data.user.accountType,
      }
    };
  } catch (error) {
    console.error('Strapi registration error:', error);
    throw error;
  }
}

// Get current user from JWT
export async function getCurrentUser(jwt: string): Promise<StrapiUser | null> {
  try {
    const response = await client.fetch('/users/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return null;
    }

    const user = await response.json();

    return {
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      accountType: user.accountType,
    };
  } catch (error) {
    console.error('Strapi getCurrentUser error:', error);
    return null;
  }
}

// Fetch business data by ID
export async function fetchBusiness(businessId: string, jwt?: string): Promise<BusinessData | null> {
  try {
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (jwt) {
      headers.Authorization = `Bearer ${jwt}`;
    }

    // Construct the URL with query parameters for the specific fields
    const url = `/businesses/${businessId}`;

    

    const response = await client.fetch(url, {
      method: 'GET',
      headers,
    });

    console.log(response.status, 'GET', url);

    if (!response.ok) {
      console.error('Failed to fetch business:', response.status, response.statusText);    
      return null;
    }

    const result = await response.json();
    // console.log('Strapi response:', JSON.stringify(result, null, 2));

    const business = result.data;

    if (!business) {
      console.error('No business data found in response');
      return null;
    }

    const businessData = {
      id: business.id.toString(),
      name: business.name,
      owner: business.owner,
      completed_appointments: business.completed_appointments || 0,
      completed_appointments_percentage: business.completed_appointments_percentage || "0%",
      pending_bookings: business.pending_bookings || 0,
      pending_bookings_percentage: business.pending_bookings_percentage || "0%",
    };

    
    return businessData;
  } catch (error) {
    console.error('Strapi fetchBusiness error:', error);
    return null;
  }
}

// Export the client for direct use if needed
export { client };
