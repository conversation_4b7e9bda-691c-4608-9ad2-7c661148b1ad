import { strapi } from '@strapi/client';

// Types for Strapi responses
interface StrapiUser {
  id: string;
  username: string;
  email: string;
  accountType: 'client' | 'business';
}

interface StrapiAuthResponse {
  jwt: string;
  user: StrapiUser;
}

interface BusinessData {
  id: string;
  name: string;
  owner: string;
  completed_appointments: number;
  completed_appointments_percentage: string;
  pending_bookings: number;
  pending_bookings_percentage: string;
}

// Create a Strapi client instance
const client = strapi({
    baseURL: 'http://strapi.agenda-servicii.ro/api',
    // Auth configuration
    auth: 'apc',
});

// Login user with email and password
export async function loginUser(email: string, password: string): Promise<StrapiAuthResponse> {
  try {
    const response = await client.fetch('/auth/local', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: email,
        password: password,
      }),
    });

    if (!response.ok) {
      throw new Error('Invalid credentials');
    }

    const data = await response.json();

    return {
      jwt: data.jwt,
      user: {
        id: data.user.id.toString(),
        username: data.user.username,
        email: data.user.email,
        accountType: data.user.accountType,
      }
    };
  } catch (error) {
    console.error('Strapi login error:', error);
    throw new Error('Invalid credentials');
  }
}

// Get current user from JWT
export async function getCurrentUser(jwt: string): Promise<StrapiUser | null> {
  try {
    const response = await client.fetch('/users/me', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwt}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return null;
    }

    const user = await response.json();

    return {
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      accountType: user.accountType,
    };
  } catch (error) {
    console.error('Strapi getCurrentUser error:', error);
    return null;
  }
}

// Fetch business data by ID
export async function fetchBusiness(businessId: string, jwt?: string): Promise<BusinessData | null> {
  try {
    console.log('Fetching business data for ID:', businessId);
    console.log('Using JWT:', jwt ? 'Yes' : 'No');

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (jwt) {
      headers.Authorization = `Bearer ${jwt}`;
    }

    // Construct the URL with query parameters for the specific fields
    const url = `/businesses/${businessId}?populate=*&fields[0]=id&fields[1]=name&fields[2]=owner&fields[3]=completed_appointments&fields[4]=completed_appointments_percentage&fields[5]=pending_bookings&fields[6]=pending_bookings_percentage`;

    console.log('Fetching from URL:', url);

    const response = await client.fetch(url, {
      method: 'GET',
      headers,
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      console.error('Failed to fetch business:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error response body:', errorText);
      return null;
    }

    const result = await response.json();
    console.log('Strapi response:', JSON.stringify(result, null, 2));

    const business = result.data;

    if (!business) {
      console.error('No business data found in response');
      return null;
    }

    const businessData = {
      id: business.id.toString(),
      name: business.attributes.name,
      owner: business.attributes.owner,
      completed_appointments: business.attributes.completed_appointments || 0,
      completed_appointments_percentage: business.attributes.completed_appointments_percentage || "0%",
      pending_bookings: business.attributes.pending_bookings || 0,
      pending_bookings_percentage: business.attributes.pending_bookings_percentage || "0%",
    };

    console.log('Processed business data:', businessData);
    return businessData;
  } catch (error) {
    console.error('Strapi fetchBusiness error:', error);
    return null;
  }
}

// Export the client for direct use if needed
export { client };
