{"include": ["**/*", "**/.server/**/*", "**/.client/**/*", ".react-router/types/**/*"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["node", "vite/client"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "rootDirs": ["app", "./.react-router/types/app"], "baseUrl": ".", "paths": {"~/*": ["./app/*"]}, "esModuleInterop": true, "verbatimModuleSyntax": true, "noEmit": true, "sourceMap": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true}}