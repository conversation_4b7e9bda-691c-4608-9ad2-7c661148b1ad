import { createCookieSessionStorage, redirect } from "react-router";
import { getCurrentUser } from "./strapi.server";
import { ENV } from "./env.server";

// Define session data types
type SessionData = {
  jwt: string;
  userId: string;
  accountType: 'client' | 'business';
};

type SessionFlashData = {
  error: string;
  success: string;
};

// Create cookie session storage
const { getSession, commitSession, destroySession } =
  createCookieSessionStorage<SessionData, SessionFlashData>({
    cookie: {
      name: "__session",
      httpOnly: true,
      maxAge: 60 * 60 * 24 * 7, // 1 week
      path: "/",
      sameSite: "lax",
      secrets: [ENV.SESSION_SECRET],
      secure: ENV.NODE_ENV === "production",
    },
  });

// Get the user session
export async function getUserSession(request: Request) {
  return getSession(request.headers.get("Cookie"));
}

// Get the logged in user if session exists
export async function getUser(request: Request) {
  const session = await getUserSession(request);
  const jwt = session.get("jwt");

  if (!jwt) return null;

  // Verify the JWT with <PERSON>rap<PERSON> and get the current user
  const user = await getCurrentUser(jwt);
  return user;
}

// Require user to be logged in
export async function requireUser(
  request: Request,
  redirectTo: string = new URL(request.url).pathname
) {
  const user = await getUser(request);

  if (!user) {
    const searchParams = new URLSearchParams([["redirectTo", redirectTo]]);
    throw redirect(`/login?${searchParams}`);
  }

  return user;
}

// Require specific account type
export async function requireAccountType(
  request: Request,
  accountType: 'client' | 'business',
  redirectTo: string = "/"
) {
  const session = await getUserSession(request);
  const userAccountType = session.get("accountType");

  if (userAccountType !== accountType) {
    throw redirect(redirectTo);
  }
}

// Create user session
export async function createUserSession({
  request,
  jwt,
  userId,
  accountType,
  remember = false,
  redirectTo,
}: {
  request: Request;
  jwt: string;
  userId: string;
  accountType: 'client' | 'business';
  remember?: boolean;
  redirectTo: string;
}) {
  const session = await getSession(request.headers.get("Cookie"));

  session.set("jwt", jwt);
  session.set("userId", userId);
  session.set("accountType", accountType);

  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await commitSession(session, {
        maxAge: remember
          ? 60 * 60 * 24 * 7 // 1 week
          : undefined,
      }),
    },
  });
}

// Logout user
export async function logout(request: Request, redirectTo: string = "/") {
  const session = await getUserSession(request);

  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await destroySession(session),
    },
  });
}

export { commitSession, destroySession };
