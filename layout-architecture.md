# Project Layout and Routing Architecture

This document outlines the layout and routing structure of the React router v7 application. The application is divided into three main sections: Public, Client, and Business, each managed by a dedicated layout component.

## Routing Configuration (`app/routes.ts`)

The core routing logic is defined in `app/routes.ts`, which uses `@react-router/dev/routes` to configure the application's routes. The routes are organized into three distinct groups, each associated with a specific layout.

### 1. Public Routes

- **Layout:** `PublicLayout.tsx`
- **Description:** These routes are accessible to all users and do not require authentication.
- **Routes:**
  - `/`: The main landing page.
  - `/login`: The login page.
  - `/register`: The user registration page.
  - `/forgot-password`: The password recovery page.

### 2. Client Area Routes

- **Layout:** `ClientLayout.tsx`
- **Prefix:** `/client`
- **Description:** These routes are part of the client-facing dashboard and require user authentication.
- **Routes:**
  - `/client`: The client dashboard index.
  - `/client/services`: Page for managing services.
  - `/client/appointments`: Page for managing appointments.
  - `/client/profile`: Page for managing the client's profile.

### 3. Business Area Routes

- **Layout:** `BusinessLayout.tsx`
- **Prefix:** `/business`
- **Description:** These routes are part of the business-facing dashboard and require user authentication.
- **Routes:**
  - `/business`: The business dashboard index.
  - `/business/services`: Page for managing business services.
  - `/business/appointments`: Page for managing business appointments.
  - `/business/profile`: Page for managing the business profile.

## Layout Components

The layout components define the shell and navigation for their respective sections of the application.

- **`PublicLayout.tsx`**: Provides the main structure for public pages, including a header with links to login, register, and the home page.
- **`ClientLayout.tsx`**: Provides the dashboard structure for authenticated clients, with navigation to client-specific sections.
- **`BusinessLayout.tsx`**: Provides the dashboard structure for authenticated business users, with navigation to business-specific sections.

This clear separation of layouts and routes makes the application's structure easy to understand and maintain.