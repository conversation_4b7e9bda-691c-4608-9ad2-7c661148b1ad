{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/node": "^7.2.0", "@react-router/serve": "^7.2.0", "@strapi/client": "^1.2.0", "isbot": "^5.1.17", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.2.0"}, "devDependencies": {"@react-router/dev": "^7.2.0", "@tailwindcss/vite": "^4.0.0", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "react-router-devtools": "^1.1.0", "tailwindcss": "^4.0.0", "typescript": "^5.8.3", "vite": "^5.4.11", "vite-tsconfig-paths": "^5.1.4"}}