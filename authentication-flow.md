# Application Authentication and Authorization Flow

This document details the end-to-end authentication and authorization process of the application, covering user registration, login, session management, and access control.

## 1. Overview

The application's authentication is built on a token-based strategy using a Remix frontend and a Strapi backend.

-   **Frontend (Remix)**: Manages UI, handles user input, and stores the authentication token in a server-side session cookie.
-   **Backend (Strapi)**: Acts as the authentication provider, managing user accounts, validating credentials, and issuing JSON Web Tokens (JWT).

The core logic is encapsulated in three key files:
-   [`app/routes/login.tsx`](app/routes/login.tsx): Handles the user-facing login form and action.
-   [`app/utils/session.server.ts`](app/utils/session.server.ts): Manages server-side session storage, including creating, reading, and destroying user sessions.
-   [`app/utils/strapi.server.ts`](app/utils/strapi.server.ts): Interacts with the Strapi API for authentication and data fetching.

## 2. Registration Flow

The registration workflow, intended to be handled by [`app/routes/register.tsx`](app/routes/register.tsx), is **not yet implemented**. The file exists as a placeholder without any logic to handle user registration.

## 3. Login Flow

The login process begins when a user submits their credentials through the form at the `/login` route.

```mermaid
sequenceDiagram
    participant User
    participant Browser
    participant ReactRouterServer as React Router Server
    participant StrapiServer as Strapi Backend

    User->>Browser: Fills and submits login form at /login
    Browser->>ReactRouterServer: POST /login with email, password, accountType
    ReactRouterServer->>ReactRouterServer: action() in app/routes/login.tsx is invoked
    ReactRouterServer->>StrapiServer: Calls loginUser(email, password) in strapi.server.ts
    StrapiServer->>StrapiServer: POST /api/auth/local
    alt Credentials Valid
        StrapiServer-->>ReactRouterServer: Returns { jwt, user }
        ReactRouterServer->>ReactRouterServer: Verifies user.accountType
        ReactRouterServer->>ReactRouterServer: Calls createUserSession() in session.server.ts
        ReactRouterServer-->>Browser: Redirect to dashboard with Set-Cookie header (__session)
        Browser->>User: Renders dashboard (/business or /client)
    else Credentials Invalid
        StrapiServer-->>ReactRouterServer: Returns error
        ReactRouterServer-->>Browser: Re-renders login form with error message
    end
```

**Step-by-step Breakdown:**

1.  **Form Submission**: The user enters their email, password, and selects an account type (`client` or `business`) on the page rendered by [`app/routes/login.tsx`](app/routes/login.tsx).
2.  **Remix Action**: The form submission triggers the `action` function in [`app/routes/login.tsx`](app/routes/login.tsx:26).
3.  **Strapi Authentication**: The action calls the `loginUser` function from [`app/utils/strapi.server.ts`](app/utils/strapi.server.ts:34), which sends a `POST` request with the user's credentials to the `/auth/local` endpoint of the Strapi backend.
4.  **JWT Issuance**: If the credentials are valid, Strapi returns a JWT and the user's data.
5.  **Session Creation**: The `action` function then calls `createUserSession` from [`app/utils/session.server.ts`](app/utils/session.server.ts:78). This function creates a `__session` cookie containing the `jwt`, `userId`, and `accountType`.
6.  **Redirection**: The user is redirected to their respective dashboard (`/business` or `/client`) with the `Set-Cookie` header in the response, establishing the session.

## 4. Session Management and Authenticated Requests

Once a user is logged in, their session is maintained via the `__session` cookie. This cookie is automatically sent with every subsequent request to the Remix server.

To access protected data or perform authenticated actions:

1.  **Loader Execution**: A route's `loader` function calls `getUser` from [`app/utils/session.server.ts`](app/utils/session.server.ts:37).
2.  **Session Reading**: `getUser` retrieves the session data from the request's cookie header.
3.  **JWT Validation**: It extracts the JWT from the session and calls `getCurrentUser` in [`app/utils/strapi.server.ts`](app/utils/strapi.server.ts:69).
4.  **Strapi Verification**: `getCurrentUser` makes a request to the `/users/me` endpoint in Strapi, sending the JWT in the `Authorization` header. Strapi validates the JWT and returns the corresponding user's data.
5.  **Data Access**: If the JWT is valid, the user data is returned to the loader, which can then fetch protected resources from Strapi using the JWT.

## 5. Access Control

Route-level access control is enforced within Remix loaders using utility functions from [`app/utils/session.server.ts`](app/utils/session.server.ts).

-   **`requireUser(request)`**: Ensures that a user is logged in. If no valid session is found, it redirects the user to the `/login` page.
-   **`requireAccountType(request, accountType)`**: Checks if the logged-in user's `accountType` (stored in the session) matches the required type for the route. If it doesn't match, it redirects the user.

## 6. Logout Flow

The logout mechanism is defined but not fully implemented on the frontend.

1.  **Intended Action**: The user would navigate to the `/logout` route.
2.  **Backend Logic**: The `action` in [`app/routes/logout.tsx`](app/routes/logout.tsx) should call the `logout` function from [`app/utils/session.server.ts`](app/utils/session.server.ts:111).
3.  **Session Destruction**: The `logout` function calls `destroySession`, which instructs the browser to clear the `__session` cookie.
4.  **Redirection**: The user is redirected to the homepage (`/`).