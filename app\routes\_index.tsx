import * as React from 'react';
import { Link } from "react-router";
import type { Route } from "./+types/_index";
export async function loader({ params }: Route.LoaderArgs) {
  const business = {
    id: "1",
    name: "Business Name",
    owner:"<PERSON>",
    appointments: [
      { id: "1", name: "Service 1", price: 20 },
      { id: "2", name: "Service 2", price: 30 },
      { id: "3", name: "Service 3", price: 40 },
    ],
    completed_appointments:87,
    completed_appointments_percentage:"+5%",
    pending_bookings:12,
    pending_bookings_percentage:"-2%"
  }
  return business;
}
export default function Index() {
  return (
    <div className="flex flex-col items-center gap-8 py-8">
       <div className="container">
        <div className="header">
            <div className="profile-info">
                <div className="avatar-placeholder"></div>
                <div className="welcome-text">
                    <PERSON><br />
                    Welcome Back!
                </div>
            </div>
            <span>12:30</span>
        </div>

        <h2>Explore Services</h2>

        <div className="explore-nav">
            <div className="explore-item">
                <img src="placeholder_star.png" alt="Featured" />
                <span>Featured</span>
            </div>
            <div className="explore-item">
                <img src="placeholder_search.png" alt="Search" />
                <span>Search</span>
            </div>
            <div className="explore-item">
                <img src="placeholder_categories.png" alt="Categories" />
                <span>Categories</span>
            </div>
        </div>

        <div className="featured-services">
            <h3 className="featured-title">Featured Services</h3>
            <div className="featured-grid">
                <div className="featured-card">
                    <span className="featured-label">New</span>
                    <div className="featured-image-placeholder">Image 1</div>
                    <div className="service-name">Service 1</div>
                    <div className="service-price">Price: $20</div>
                    <div className="rating">⭐⭐⭐⭐⭐</div>
                </div>
                <div className="featured-card">
                    <span className="featured-label">Discount!</span>
                    <div className="featured-image-placeholder">Image 2</div>
                    <div className="service-name">Service 2</div>
                    <div className="service-price">Price: $30</div>
                    <div className="rating">⭐⭐⭐⭐</div>
                </div>
            </div>
        </div>

        <div className="customer-reviews">
            <h3 className="reviews-title">Customer Reviews</h3>
            <div className="review-item">
                <div className="reviewer-info">
                    <div className="reviewer-avatar-placeholder"></div>
                    <span>#Alfie</span>
                </div>
                <div className="review-text">Great service! Highly recommended.</div>
                <div className="rating">⭐⭐⭐⭐⭐</div>
                <div className="review-actions">
                    <button className="like-button">👍</button>
                    <button className="dislike-button">👎</button>
                </div>
            </div>
            <div className="review-item">
                <div className="reviewer-info">
                    <div className="reviewer-avatar-placeholder"></div>
                    <span>Bob</span>
                </div>
                <div className="review-text">Excellent experience again.</div>
                <div className="rating">⭐⭐⭐⭐</div>
                <div className="review-actions">
                    <button className="like-button">👍</button>
                    <button className="dislike-button">👎</button>
                </div>
            </div>
        </div>

        <div className="popular-services">
            <h3 className="popular-title">Popular Services</h3>
            <div className="popular-item">
                <div className="popular-info">
                    <div className="popular-icon-placeholder">⭐</div>
                    <div className="popular-details">
                        <div className="popular-name">Service 3</div>
                        <div className="popular-location">Location: Downtown</div>
                    </div>
                </div>
                <div className="popular-actions">
                    <span className="popular-price">Price: $25</span>
                    <span className="phone-icon">📞</span>
                    <span className="cart-icon">🛒</span>
                </div>
            </div>
            <div className="popular-item">
                <div className="popular-info">
                    <div className="popular-icon-placeholder">⭐</div>
                    <div className="popular-details">
                        <div className="popular-name">Service 4</div>
                        <div className="popular-location">Location: Uptown</div>
                    </div>
                </div>
                <div className="popular-actions">
                    <span className="popular-price">Price: $35</span>
                    <span className="phone-icon">📞</span>
                    <span className="cart-icon">🛒</span>
                </div>
            </div>
        </div>

        <div className="bottom-buttons">
            <a href="#" className="learn-more-button">Learn More</a>
            <a href="#" className="book-now-button">Book Now</a>
        </div>

        <div className="latest-posts">
            <h3 className="posts-title">Latest Posts</h3>
            <div className="post-card">
                <div className="post-header">
                    <span>Carol</span>
                    <span>...</span>
                </div>
                <div className="post-image-placeholder">Post Image</div>
                <p>Excited to try out new services!</p>
                <div className="post-actions">
                    <span>❤️</span>
                    <span className="hashtag">#New</span>
                    <span className="hashtag">#Exciting</span>
                </div>
            </div>
        </div>
    </div>
      <p className="text-xl text-center max-w-2xl">
        Your one-stop solution for managing service appointments and schedules.
      </p>
      
      <div className="flex gap-4 mt-4">
        <Link 
          to="/login" 
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
        >
          Login
        </Link>
        <Link 
          to="/register" 
          className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
        >
          Register
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
        <div className="p-6 border rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">For Clients</h2>
          <p>Book appointments, get reminders, and manage your services.</p>
        </div>
        <div className="p-6 border rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">For Businesses</h2>
          <p>Manage your services, appointments, and client relationships.</p>
        </div>
        <div className="p-6 border rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Easy to Use</h2>
          <p>Simple interface for both clients and service providers.</p>
        </div>
      </div>
    </div>
  );
}
