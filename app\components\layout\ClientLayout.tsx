import { Link, Outlet } from "react-router";

export default function ClientLayout() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="bg-green-600 text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/client" className="text-xl font-bold">
            Client Dashboard
          </Link>
          <nav className="flex gap-4">
            <Link to="/client" className="hover:underline">
              Dashboard
            </Link>
            <Link to="/client/services" className="hover:underline">
              Services
            </Link>
            <Link to="/client/appointments" className="hover:underline">
              Appointments
            </Link>
            <Link to="/client/profile" className="hover:underline">
              Profile
            </Link>
            <Link to="/" className="hover:underline">
              Logout
            </Link>
          </nav>
        </div>
      </header>
      <main className="flex-grow container mx-auto p-4">
        <Outlet />
      </main>
      <footer className="bg-gray-100 p-4">
        <div className="container mx-auto text-center text-gray-600">
          &copy; {new Date().getFullYear()} Service Agenda - Client Area
        </div>
      </footer>
    </div>
  );
}

