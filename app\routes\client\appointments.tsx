import * as React from 'react';
import type { Route } from "./+types/appointments";
export async function loader({ params }: Route.LoaderArgs) {
  const info = {
    
    appointments: [
      { id: "1", name: "Service 1", date: "2023-09-15", time: "10:00 AM", status: "Pending" },
      { id: "2", name: "Service 2", date: "2023-09-16", time: "11:30 AM", status: "Confirmed" },
      { id: "3", name: "Service 3", date: "2023-09-17", time: "2:00 PM", status: "Completed" }
    ],
    analytics: {
      total_appointments: 50,
      total_procent:"+10%",
      average_rating: 4.5,
    },
    messages: [
      { id: "1", sender: "<PERSON>", message: "Hello, how can I help you?", timestamp: "2023-09-15 10:00" },
      { id: "2", sender: "Support", message: "Hello, how can I help you?", timestamp: "2023-09-15 10:05" },
    ],
    customer_reviews: [
      { id: "1", reviewer: "<PERSON>", review: "Great service! Will definitely come back.", rating: 5 },
      { id: "2", reviewer: "<PERSON>", review: "Very friendly staff and great service.", rating: 4 },
    ]
  }
  return info;
}
export default function ClientAppointments({
  loaderData,
}: Route.ComponentProps) {
  return (
    <div>
      <div className="container">
        <h1 className="header">My Appointments & Reminders</h1>

        <div className="appointments-list">
            <h2 className="list-title">Appointments List</h2>
            {loaderData.appointments.map((appointment:any) => (
            <div className="appointment-item">
                <div className="calendar-icon-placeholder">17</div>
                <div className="appointment-details">
                    <div className="service-name">{appointment.name}</div>
                    <div className="date-time">{appointment.date} {appointment.time}</div>
                </div>
                <div className="status-label status-confirmed">Status: {appointment.status}</div>
                <div>
                    <button className="action-icon-button">🔎</button>
                    <button className="action-icon-button delete">❌</button>
                </div>
            </div>
            ))}
            
        </div>

        <div className="upcoming-reminders">
            <h2 className="list-title">Upcoming Reminders</h2>
            <div style={{ fontSize: '0.9em', color: '#777' }}>(No upcoming reminders)</div>
        </div>

        <div className="reminders-notifications">
            <h2 className="list-title">Reminders & Notifications</h2>
            <div className="reminder-item">
                <div className="reminder-icon-placeholder">⏰</div>
                <div className="reminder-details">
                    <div className="reminder-title">Appointment Reminder</div>
                    <div>Today, 2:30 PM</div>
                </div>
                <button className="notification-icon-button">🔔</button>
            </div>
            <div className="reminder-item">
                <div className="reminder-icon-placeholder">💬</div>
                <div className="reminder-details">
                    <div className="reminder-title">Message</div>
                     {loaderData.messages.map((message:any) => (


                    <div>{message.message}</div>
                     ))}
                </div>
                <button className="notification-icon-button">🔔</button>
            </div>
        </div>

        <a href="#" className="notification-settings-button">Notification Settings</a>

        <div className="customer-reviews">
            <h2 className="list-title">Customer Reviews</h2>
            {loaderData.customer_reviews.map((review:any) => (
            <div className="review-card">
                <div className="reviewer-info">
                    <div className="reviewer-avatar-placeholder"></div>
                    <span className="reviewer-name">{review.reviewer}</span>
                    <div className="rating">{Array(review.rating).fill('⭐').join('')}</div>
                </div>
                <p>{review.review}</p>
            </div>
            ))}
            
            
               
        </div>

        <div className="appointment-analytics">
            <div className="analytics-header">
                <h3>Appointment Analytics</h3>
                <button className="view-details-button">View Details &gt;</button>
            </div>
            <div className="analytics-grid">
                <div className="analytics-card">
                    <div className="analytics-value">({loaderData.analytics.total_appointments})</div>
                    <div className="analytics-label">Total Appointments</div>
                    <div className="analytics-trend">{loaderData.analytics.total_procent}</div>
                </div>
                <div className="analytics-card">
                    <div className="analytics-value">{loaderData.analytics.average_rating}</div>
                    <div className="analytics-label">Average Rating</div>
                </div>
            </div>
        </div>
      </div>
    </div>
  );
}

