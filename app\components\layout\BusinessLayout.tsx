import { Link, Outlet } from "react-router";

export default function BusinessLayout() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="bg-purple-600 text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/business" className="text-xl font-bold">
            Business Dashboard
          </Link>
          <nav className="flex gap-4">
            <Link to="/business" className="hover:underline">
              Dashboard
            </Link>
            <Link to="/business/services" className="hover:underline">
              Services
            </Link>
            <Link to="/business/appointments" className="hover:underline">
              Appointments
            </Link>
            <Link to="/business/profile" className="hover:underline">
              Profile
            </Link>
            <Link to="/" className="hover:underline">
              Logout
            </Link>
          </nav>
        </div>
      </header>
      <main className="flex-grow container mx-auto p-4">
        <Outlet />
      </main>
      <footer className="bg-gray-100 p-4">
        <div className="container mx-auto text-center text-gray-600">
          &copy; {new Date().getFullYear()} Service Agenda - Business Area
        </div>
      </footer>
    </div>
  );
}

