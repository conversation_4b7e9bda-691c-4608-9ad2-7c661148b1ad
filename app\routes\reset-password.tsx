import { Link, Form, useActionData, useNavigation, redirect, useLoaderData } from "react-router";

export async function loader({ request }: { request: Request }) {

}

export async function action({ request }: { request: Request }) {

}

export default function ResetPassword() {
  
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  return (
    <div className="max-w-md mx-auto mt-8 p-6 border rounded-lg shadow-sm">
      <h1 className="text-2xl font-bold mb-6 text-center">Reset Password</h1>
      
  
    </div>
  );
}
