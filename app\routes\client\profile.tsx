import * as React from 'react';

export default function ClientProfile() {
  return (
    <div>
      <div className="container">
        <h1 className="header">Profile & Feedback</h1>

        <div className="profile-header">
            <div className="avatar-placeholder"></div>
            <div className="profile-info">
                <h2 className="user-name"><PERSON></h2>
                <p className="edit-profile">Edit Profile Details</p>
            </div>
        </div>

        <div className="actions-bar">
            <button className="action-button">
                <img src="placeholder_user.png" alt="User Profile" />
                User Profile
            </button>
            <button className="action-button">
                <img src="placeholder_star.png" alt="Review History" />
                Review History
            </button>
        </div>

        <div className="section-title">Personal Info</div>
        <input type="text" className="input-field" placeholder="Enter your personal information" />

        <div className="section-title">Contact Preferences</div>
        <input type="text" className="input-field" placeholder="Specify your contact preferences" />

        <div className="section-title">Linked Payment Methods</div>
        <input type="text" className="input-field" placeholder="Add linked payment methods" />

        <button className="submit-button">Submit</button>

        <div className="section-title">Recent Reviews</div>
        <div className="recent-reviews">
            <div className="review-card">
                <div className="reviewer-info">
                    <div className="reviewer-avatar-placeholder"></div>
                    <span className="reviewer-name">Jane Smith</span>
                    <div className="rating">⭐⭐⭐⭐⭐</div>
                </div>
                <p>Great service and very professional</p>
            </div>
            <div className="review-card">
                <div className="reviewer-info">
                    <div className="reviewer-avatar-placeholder"></div>
                    <span className="reviewer-name">Michael Johns</span>
                </div>
                <p>Highly recommen...</p>
            </div>
        </div>
      </div>
    </div>
  );
}

