import { Link, Form, useActionData, useNavigation, redirect } from "react-router";

import type { Route } from "./+types/forgot-password";

export async function loader({ request }: Route.LoaderArgs) {
  
}

export async function action({ request }: Route.ActionArgs) {
  }

export default function ForgotPassword() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  
  return (
    <div className="max-w-md mx-auto mt-8 p-6 border rounded-lg shadow-sm">
      <h1 className="text-2xl font-bold mb-6 text-center">Forgot Password</h1>

    </div>
  );
}
