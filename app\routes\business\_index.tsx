import * as React from 'react';
import type { Route } from "./+types/_index";
import { fetchBusiness } from "../../utils/strapi.server";
import { getUserSession } from "../../utils/session.server";

export async function loader({ request }: Route.LoaderArgs) {
  console.log('Business dashboard loader called');

  // Get the JWT from session
  const session = await getUserSession(request);
  const jwt = session.get("jwt");

  console.log('Session JWT available:', jwt ? 'Yes' : 'No');

  // For now, we'll use a hardcoded business ID. In a real app, this would come from the user's profile
  const businessId = "1";

  // Fetch business data from Strapi
  console.log('Attempting to fetch business data from Strapi...');
  const businessData = await fetchBusiness(businessId, jwt || undefined);

  console.log('Strapi fetch result:', businessData ? 'Success' : 'Failed');

  // Fallback to mock data if Strapi fetch fails
  const business = businessData || {
    id: "1",
    name: "Business Name (Mock Data)",
    owner: "<PERSON>",
    completed_appointments: 87,
    completed_appointments_percentage: "+5%",
    pending_bookings: 12,
    pending_bookings_percentage: "-2%"
  };

  console.log('Final business data:', business);

  // Add mock appointments data (this would typically come from a separate API call)
  const businessWithAppointments = {
    ...business,
    appointments: [
      { id: "1", name: "Service 1", price: 20 },
      { id: "2", name: "Service 2", price: 30 },
      { id: "3", name: "Service 3", price: 40 },
    ],
  };

  return businessWithAppointments;
}

export default function BusinessDashboard({
  loaderData,
}: Route.ComponentProps) {
  return (
    <div>
      <div className="dashboard-container">
        <div className="header">
          <h2>{loaderData.name}</h2>

        </div>

        <div className="app-bar">
          <div className="app-bar-item">
            <img src="placeholder_dashboard.png" alt="Dashboard"/>
            <span>Dashboard</span>
          </div>
          <div className="app-bar-item">
            <img src="placeholder_appointments.png" alt="Appointments"/>
            <span>Appointments</span>
          </div>
          <div className="app-bar-item">
            <img src="placeholder_services.png" alt="Services"/>
            <span>Services</span>
          </div>
          <div className="app-bar-item">
            <img src="placeholder_calendar.png" alt="Calendar"/>
            <span>Calendar</span>
          </div>
          <div className="app-bar-item">
            <img src="placeholder_reviews.png" alt="Reviews"/>
            <span>Reviews</span>
          </div>
          <div className="app-bar-item">
            <img src="placeholder_settings.png" alt="Settings"/>
            <span>Settings</span>
          </div>
        </div>

        <div className="business-info">
          <div className="avatar"></div>
          <div>
            <strong>{loaderData.owner}</strong>
            <p style={{ fontSize: '0.9em', color: '#555', marginTop: '2px' }}>
              Manage your business efficiently
            </p>
          </div>
        </div>

        <h3>Summary Cards</h3>
        <div className="summary-cards-container">
          <div className="summary-card">

            <div className="summary-card">
              {loaderData.appointments.map((appointment:any) => (
                <div key={appointment.id} className="appointment-item">
                  {appointment.name} - ${appointment.price}
                </div>
              ))}
            </div>
            <a href="#" style={{ fontSize: '0.8em', color: 'blue', textDecoration: 'none' }}>
              View upcoming a...
            </a>
          </div>



        </div>

        <div className="actions-container">
          <a href="#" className="action-button">Set Availability</a>
          <a href="#" className="action-button">View Appointment Details</a>
          <a href="#" className="action-button primary">Add New Service</a>
        </div>

        <h3>Business Insights</h3>
        <div className="business-insights-container">
          <div className="insight-card">
            <div className="insight-title">Completed Appoint...</div>
    <div className="insight-value">{loaderData.completed_appointments}</div>
            <div className="insight-trend">{loaderData.completed_appointments_percentage}</div>
          </div>
          <div className="insight-card">
            <div className="insight-title">Pending Bookings</div>
            <div className="insight-value">{loaderData.pending_bookings}</div>
            <div className="insight-trend" style={{ color: loaderData.pending_bookings_percentage.startsWith('-') ? 'red' : 'green' }}>{loaderData.pending_bookings_percentage}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
