import * as React from 'react';
import type { Route } from "./+types/services";
export async function loader({ params }: Route.LoaderArgs) {
  const services = [
    { id: 1, name: 'Haircut', price: 20 , durationn: 30 },
    { id: 2, name: 'Manicure', price: 15 , durationn: 45 }
  ];
  return {services: services};
}
export default function BusinessServices({
  loaderData,
}: Route.ComponentProps) {
  return (
    <div>
      <div className="container">
        <div className="top-bar">
            <h2>Service & Availability Management</h2>
            <div className="top-bar-icons">
                <div className="icon-box">
                    <span className="icon">🛠️</span>
                    <span>Service List</span>
                </div>
                <div className="icon-box">
                    <span className="icon">🗓️</span>
                    <span>Availability Setup</span>
                </div>
            </div>
        </div>

        <div className="services-header">
            <h3>Services</h3>
            <button className="add-service-button">Add Service &gt;</button>
        </div>

 {loaderData.services.map((service:any) => ( 
<div className="service-item">
            <strong>{service.name}</strong>
            <div className="service-details">Price:{service.price}, Duration{service.durationn} mins</div>
        </div>

 ))}
        
        



        <div className="bottom-actions">
            <button className="cancel-button">Cancel</button>
            <button className="save-button">Save</button>
        </div>
      </div>
    </div>
  );
}
