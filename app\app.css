@import "tailwindcss";

@theme {
  --font-sans: "Inter", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }

        .dashboard-container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .app-bar {
            background-color: #eee;
            padding: 10px;
            border-radius: 6px;
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
        }

        .app-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.8em;
            color: #555;
        }

        .app-bar-item img {
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .business-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .avatar {
            width: 50px;
            height: 50px;
            background-color: #ddd;
            border-radius: 50%;
            margin-right: 10px;
        }

        .summary-cards-container {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            overflow-x: auto; /* For potential scrolling if more cards are added */
        }

        .summary-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            width: 200px; /* Adjust as needed */
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .summary-card-title {
            font-size: 0.9em;
            color: #777;
            margin-bottom: 5px;
        }

        .summary-card-value {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .actions-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .action-button {
            background-color: #eee;
            color: #333;
            border: none;
            padding: 12px 15px;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            text-decoration: none;
            display: block;
        }

        .action-button.primary {
            background-color: #212121; /* Example primary color */
            color: #fff;
        }

        .business-insights-container {
            display: flex;
            gap: 15px;
        }

        .insight-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            width: 150px; /* Adjust as needed */
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .insight-title {
            font-size: 0.8em;
            color: #777;
            margin-bottom: 5px;
        }

        .insight-value {
            font-size: 1.1em;
            font-weight: bold;
        }

        .insight-trend {
            font-size: 0.75em;
            color: green; /* Or red for negative trends */
        }
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
        }

        .container {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            width: 400px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .top-bar {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .top-bar-icons {
            display: flex;
            justify-content: space-around;
            width: 100%;
        }

        .icon-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #555;
        }

        .icon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .services-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            color: #333;
        }

        .add-service-button {
            background-color: #f0f0f0;
            color: #333;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
        }

        .service-item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 4px;
            color: #555;
            font-size: 14px;
        }

        .location-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .location-card {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
            color: #666;
        }

        .location-header {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .image-placeholder {
            background-color: #e0e0e0;
            height: 80px;
            margin-bottom: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #888;
            border-radius: 4px;
            font-size: 12px;
        }

        .service-details {
            font-size: 12px;
            margin-bottom: 5px;
        }

        .delete-button {
            background: none;
            border: none;
            color: #ff4d4d;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            margin-top: 5px;
        }

        .bottom-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: flex-end;
        }

        .cancel-button {
            background-color: #fff;
            color: #555;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 16px;
        }

        .save-button {
            background-color: #333;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 10px 15px;
            cursor: pointer;
            font-size: 16px;
        }
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }

        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 400px; /* Adjust as needed */
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .app-bar {
            background-color: #e0f2f7; /* Light cyan */
            padding: 10px;
            border-radius: 6px;
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
            border: 1px dashed #81d4fa; /* Cyan 200 */
        }

        .app-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.8em;
            color: #1e88e5; /* Blue 600 */
        }

        .app-bar-item img {
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        .services-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .services-title {
            font-weight: bold;
            color: #333;
        }

        .add-service-button {
            background-color: #f5f5f5;
            color: #555;
            border: 1px solid #ccc;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8em;
            cursor: pointer;
        }

        .service-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .service-details {
            font-size: 0.8em;
            color: #777;
        }

        .locations-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .location-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .location-title {
            font-size: 0.9em;
            color: #555;
            margin-bottom: 8px;
        }

        .image-placeholder {
            background-color: #eee;
            height: 80px;
            border-radius: 4px;
            margin-bottom: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #999;
            font-size: 0.8em;
        }

        .service-info {
            font-size: 0.85em;
            color: #333;
            margin-bottom: 5px;
        }

        .edit-delete-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .edit-button, .delete-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            font-size: 1em;
            color: #f44336; /* Red 500 */
        }

        .bottom-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .cancel-button {
            background-color: #f5f5f5;
            color: #555;
            border: 1px solid #ccc;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .save-button {
            background-color: #212121; /* Black */
            color: #fff;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
        }
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }

        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 400px; /* Adjust as needed */
            margin: 0 auto;
        }

        .header {
            text-align: left;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .appointments-list {
            margin-bottom: 20px;
        }

        .list-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .appointment-item {
            display: grid;
            grid-template-columns: 50px 1fr auto auto;
            gap: 10px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            align-items: center;
        }

        .appointment-item:last-child {
            border-bottom: none;
        }

        .calendar-icon-placeholder {
            width: 30px;
            height: 30px;
            background-color: #fbe9e7; /* Red 50 */
            color: #d32f2f; /* Red 700 */
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 0.8em;
            font-weight: bold;
        }

        .appointment-details {
            font-size: 0.9em;
            color: #555;
        }

        .status-label {
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-confirmed {
            color: green;
        }

        .status-pending {
            color: orange;
        }

        .action-icon-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2em;
            color: #777;
            padding: 0;
            margin-left: 5px;
        }

        .action-icon-button.delete {
            color: #f44336; /* Red 500 */
        }

        .upcoming-reminders {
            margin-bottom: 20px;
        }

        .reminders-notifications {
            margin-bottom: 20px;
        }

        .reminder-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .reminder-item:last-child {
            border-bottom: none;
        }

        .reminder-icon-placeholder {
            width: 30px;
            height: 30px;
            background-color: #e1f5fe; /* Light blue 50 */
            color: #03a9f4; /* Light blue 600 */
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            font-size: 1em;
        }

        .reminder-details {
            flex-grow: 1;
            font-size: 0.9em;
            color: #555;
        }

        .reminder-title {
            font-weight: bold;
            color: #333;
        }

        .notification-icon-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.2em;
            color: #ffc107; /* Amber 500 */
            padding: 0;
            margin-left: 10px;
        }

        .notification-settings-button {
            background-color: #212121; /* Black */
            color: #fff;
            border: none;
            padding: 12px 15px;
            border-radius: 6px;
            font-size: 0.9em;
            cursor: pointer;
            width: 100%;
            text-align: center;
            text-decoration: none;
            display: block;
            margin-bottom: 20px;
        }

        .customer-reviews {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .review-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 10px;
            flex: 1;
            font-size: 0.8em;
            color: #555;
        }

        .reviewer-info {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .reviewer-avatar-placeholder {
            width: 25px;
            height: 25px;
            background-color: #ddd;
            border-radius: 50%;
            margin-right: 8px;
        }

        .reviewer-name {
            font-weight: bold;
            color: #333;
            margin-right: 5px;
        }

        .rating {
            color: gold;
            font-size: 0.9em;
        }

        .appointment-analytics {
            margin-bottom: 20px;
        }

        .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .view-details-button {
            background-color: #f5f5f5;
            color: #555;
            border: 1px solid #ccc;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8em;
            cursor: pointer;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .analytics-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
        }

        .analytics-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .analytics-label {
            font-size: 0.8em;
            color: #777;
        }

        .analytics-trend {
            font-size: 0.75em;
            color: green; /* Or red for negative trends */
        }
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }

        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 400px; /* Adjust as needed */
            margin: 0 auto;
        }

        .header {
            background-color: #e0f7fa; /* Light cyan 50 */
            color: #00bcd4; /* Cyan 500 */
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }

        .service-info-section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 6px;
        }

        .service-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .avatar-placeholder {
            width: 30px;
            height: 30px;
            background-color: #ddd;
            border-radius: 50%;
            margin-right: 10px;
        }

        .service-name {
            font-weight: bold;
            color: #333;
        }

        .service-category {
            font-size: 0.8em;
            color: #777;
        }

        .image-container {
            position: relative;
            margin-bottom: 10px;
        }

        .discount-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: #ffcdd2; /* Red 100 */
            color: #f44336; /* Red 500 */
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .high-quality-image-placeholder {
            background-color: #eee;
            height: 150px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #999;
            font-size: 0.9em;
            text-align: center;
            width: 100%;
        }

        .service-details {
            font-size: 0.9em;
            color: #555;
            margin-bottom: 8px;
        }

        .emoji {
            font-size: 1.2em;
        }

        .map-container {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 6px;
        }

        .service-location-map-placeholder {
            background-color: #ddd;
            height: 120px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #777;
            font-size: 0.9em;
            width: 100%;
        }

        .reviews-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .review-card {
            background-color: #f9f9f9;
            border-radius: 6px;
            padding: 10px;
            flex: 1;
            font-size: 0.8em;
            color: #555;
        }

        .reviewer-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .rating {
            color: gold;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            display: block;
            font-size: 0.9em;
            color: #333;
            margin-bottom: 5px;
        }

        .input-field {
            width: calc(100% - 12px);
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .notes-field {
            width: calc(100% - 12px);
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
            resize: vertical;
            min-height: 60px;
        }

        .book-appointment-button {
            background-color: #212121; /* Black */
            color: #fff;
            border: none;
            padding: 12px 15px;
            border-radius: 6px;
            font-size: 0.9em;
            cursor: pointer;
            width: 100%;
            text-align: center;
            text-decoration: none;
            display: block;
        }
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }

        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 400px; /* Adjust as needed */
            margin: 0 auto;
        }

        .header {
            text-align: left;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .avatar-placeholder {
            width: 50px;
            height: 50px;
            background-color: #ddd;
            border-radius: 50%;
            margin-right: 10px;
        }

        .profile-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: bold;
            color: #333;
        }

        .edit-profile {
            font-size: 0.8em;
            color: #777;
        }

        .actions-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .action-button {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 10px 15px;
            text-align: center;
            flex-grow: 1;
            font-size: 0.9em;
            color: #555;
        }

        .action-button img {
            width: 20px;
            height: 20px;
            margin-bottom: 5px;
        }

        .section-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .input-field {
            width: calc(100% - 12px);
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 0.9em;
            color: #555;
        }

        .input-field::placeholder {
            color: #999;
        }

        .submit-button {
            background-color: #212121; /* Black */
            color: #fff;
            border: none;
            padding: 12px 15px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            width: 100%;
            margin-bottom: 20px;
        }

        .recent-reviews {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto; /* For potential scrolling if more reviews */
        }

        .review-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 10px;
            width: 180px; /* Adjust as needed */
            flex-shrink: 0; /* Prevent shrinking */
            font-size: 0.8em;
            color: #555;
        }

        .reviewer-info {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .reviewer-avatar-placeholder {
            width: 25px;
            height: 25px;
            background-color: #ddd;
            border-radius: 50%;
            margin-right: 8px;
        }

        .reviewer-name {
            font-weight: bold;
            color: #333;
            margin-right: 5px;
        }

        .rating {
            color: gold;
            font-size: 0.9em;
        }
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }

        .container {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 400px; /* Adjust as needed */
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .profile-info {
            display: flex;
            align-items: center;
        }

        .avatar-placeholder {
            width: 30px;
            height: 30px;
            background-color: #ddd;
            border-radius: 50%;
            margin-right: 8px;
        }

        .welcome-text {
            font-size: 0.9em;
            color: #555;
        }

        .explore-nav {
            display: flex;
            justify-content: space-around;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .explore-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.8em;
            color: #333;
        }

        .explore-item img {
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
            background-color: #eee; /* Placeholder for icon background */
            border-radius: 50%;
            padding: 5px;
        }

        .featured-services {
            margin-bottom: 20px;
        }

        .featured-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .featured-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .featured-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 10px;
        }

        .featured-label {
            font-size: 0.7em;
            color: #fff;
            background-color: #673ab7; /* Purple 500 */
            padding: 3px 5px;
            border-radius: 4px;
            margin-bottom: 5px;
            display: inline-block;
        }

        .featured-image-placeholder {
            background-color: #eee;
            height: 80px;
            border-radius: 4px;
            margin-bottom: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #999;
            font-size: 0.8em;
        }

        .service-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .service-price {
            font-size: 0.9em;
            color: #007bff; /* Blue 600 */
        }

        .customer-reviews {
            margin-bottom: 20px;
        }

        .reviews-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .review-item {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            gap: 10px;
            align-items: flex-start;
        }

        .reviewer-info {
            display: flex;
            flex-direction: column;
            font-size: 0.8em;
            color: #555;
        }

        .reviewer-avatar-placeholder {
            width: 25px;
            height: 25px;
            background-color: #ddd;
            border-radius: 50%;
            margin-bottom: 3px;
        }

        .review-text {
            font-size: 0.85em;
            color: #333;
        }

        .rating {
            color: gold;
            font-size: 0.9em;
        }

        .review-actions {
            display: flex;
            gap: 5px;
            align-items: center;
            margin-top: 5px;
        }

        .like-button, .dislike-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1em;
            color: #777;
        }

        .popular-services {
            margin-bottom: 20px;
            border: 2px solid #2196f3; /* Blue 500 */
            border-radius: 8px;
            padding: 10px;
        }

        .popular-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .popular-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .popular-item:last-child {
            border-bottom: none;
        }

        .popular-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .popular-icon-placeholder {
            width: 25px;
            height: 25px;
            background-color: #ffe0b2; /* Amber 100 */
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ff9800; /* Amber 500 */
            font-size: 0.8em;
        }

        .popular-details {
            display: flex;
            flex-direction: column;
            font-size: 0.8em;
            color: #555;
        }

        .popular-name {
            font-weight: bold;
            color: #333;
        }

        .popular-price {
            font-size: 0.9em;
            color: #007bff; /* Blue 600 */
        }

        .popular-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .phone-icon {
            color: #4caf50; /* Green 500 */
        }

        .cart-icon {
            color: #f44336; /* Red 500 */
        }

        .bottom-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .learn-more-button {
            background-color: #f5f5f5;
            color: #555;
            border: 1px solid #ccc;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            flex-grow: 1;
            text-align: center;
            text-decoration: none;
            font-size: 0.9em;
        }

        .book-now-button {
            background-color: #212121; /* Black */
            color: #fff;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            flex-grow: 1;
            text-align: center;
            text-decoration: none;
            font-size: 0.9em;
        }

        .latest-posts {
            margin-bottom: 20px;
        }

        .posts-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .post-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 10px;
        }

        .post-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.8em;
            color: #555;
        }

        .post-image-placeholder {
            background-color: #eee;
            height: 150px;
            border-radius: 4px;
            margin-bottom: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #999;
            font-size: 0.8em;
            text-align: center;
        }

        .post-actions {
            display: flex;
            gap: 10px;
            align-items: center;
            font-size: 0.8em;
            color: #777;
            margin-top: 10px;
        }

        .hashtag {
            color: #007bff; /* Blue 600 */
        }