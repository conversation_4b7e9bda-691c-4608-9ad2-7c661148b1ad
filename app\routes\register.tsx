import { Link, Form, useActionData, useNavigation, redirect } from "react-router";
import { useState } from "react";

import { createUserSession, getUserSession } from "../utils/session.server";
import type { Route } from "./+types/register";


export async function loader({ request }: Route.LoaderArgs) {
}

export async function action({ request }: Route.ActionArgs) {

}

export default function Register() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  

  return (
    <div className="max-w-md mx-auto mt-8 p-6 border rounded-lg shadow-sm">
      <h1 className="text-2xl font-bold mb-6 text-center">Register</h1>

  
    </div>
  );
}


