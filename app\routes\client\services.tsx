import * as React from 'react';

export default function ClientServices() {
  return (
    <div>
      <div className="container">
        <div className="header">
            Service Details & Booking
        </div>

        <div className="service-info-section">
            <div className="service-name-row">
                <div className="avatar-placeholder"></div>
                <div>
                    <h2 className="service-name">Service Name</h2>
                    <p className="service-category">Category / Sub-Category</p>
                </div>
            </div>

            <div className="image-container">
                <span className="discount-label">Discount: XX%</span>
                <div className="high-quality-image-placeholder">High-Quality Image 1</div>
            </div>
            <p className="service-details">Service Image 1</p>
            <p className="service-details">Price: $XX, Duration: XX mins <span className="emoji">😊</span></p>
        </div>

        <div className="service-info-section">
            <div className="image-container">
                <span className="discount-label">Discount: XX%</span>
                <div className="high-quality-image-placeholder">High-Quality Image 2</div>
            </div>
            <p className="service-details">Service Image 2</p>
            <p className="service-details">Price: $XX, Duration: XX mins <span className="emoji">😊</span></p>
        </div>

        <div className="map-container">
            <div className="service-location-map-placeholder">Service Location Map</div>
        </div>

        <div className="reviews-section">
            <div className="review-card">
                <p className="reviewer-name">User 1</p>
                <div className="rating">⭐⭐⭐⭐⭐</div>
                <p>Great service! Highly recommend.</p>
            </div>
            <div className="review-card">
                <p className="reviewer-name">User 2</p>
                <p>Excellent experie... back again.</p>
            </div>
        </div>

        <div className="input-group">
            <label htmlFor="your-name" className="input-label">Your Name</label>
            <input type="text" id="your-name" className="input-field" placeholder="Enter your name" />
        </div>

        <div className="input-group">
            <label htmlFor="contact-info" className="input-label">Contact Information</label>
            <input type="text" id="contact-info" className="input-field" placeholder="Enter your email/phone number" />
        </div>

        <div className="input-group">
            <label htmlFor="notes" className="input-label">Notes</label>
            <textarea id="notes" className="notes-field" placeholder="Add any additional notes"></textarea>
        </div>

        <a href="#" className="book-appointment-button">Book Appointment</a>
      </div>
    </div>
  );
}

