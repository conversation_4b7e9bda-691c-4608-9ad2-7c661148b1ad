import * as React from 'react';
import type { Route } from "./+types/appointments";
export async function loader({ params }: Route.LoaderArgs) {
  const service  = {
    
  }
  return service;
}
export default function BusinessAppointments() {
  return (
    <div>
      <div className="container">
        <div className="header">
            <span>Business services</span>
            <span>12:30</span>
        </div>

        <h2>Service & Availability Management</h2>

        <div className="app-bar">
            <div className="app-bar-item">
                <img src="placeholder_wrench.png" alt="Service List" />
                <span>Service List</span>
            </div>
            <div className="app-bar-item">
                <img src="placeholder_calendar_17.png" alt="Availability Setup" />
                <span>Availability Setup</span>
            </div>
        </div>

        <div className="services-section">
            <h3 className="services-title">Services</h3>
            <button className="add-service-button">Add Service &gt;</button>
        </div>

        <div className="service-item">
            <div className="service-name">Haircut</div>
            <div className="service-details">Price: $20, Duration: 30 mins</div>
        </div>

        <div className="service-item">
            <div className="service-name">Manicure</div>
            <div className="service-details">Price: $15, Duration: 45 mins</div>
        </div>

        <div className="locations-grid">
            <div className="location-card">
                <h4 className="location-title">Location: Salon A</h4>
                <div className="image-placeholder">Image1</div>
                <div className="service-info">Service Name</div>
                <div className="service-info">Price: $20, Duratio...</div>
                <div className="edit-delete-actions">
                    <button className="edit-button">✏️</button>
                    <button className="delete-button">❌</button>
                </div>
            </div>
            <div className="location-card">
                <h4 className="location-title">Location: Salon B</h4>
                <div className="image-placeholder">Image2</div>
                <div className="service-info">Another Service</div>
                <div className="service-info">Price: $15, Duratio...</div>
                <div className="edit-delete-actions">
                    <button className="edit-button">✏️</button>
                    <button className="delete-button">❌</button>
                </div>
            </div>
        </div>

        <div className="bottom-actions">
            <button className="cancel-button">Cancel</button>
            <button className="save-button">Save</button>
        </div>
      </div>
    </div>
  );
}
