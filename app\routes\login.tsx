import { Link, Form, useActionData, useNavigation, redirect } from "react-router";
import { useState } from "react";
import { loginUser } from "../utils/strapi.server";
import { createUserSession, getUserSession } from "../utils/session.server";
import type { Route } from "./+types/login";

type FormErrors = {
  email?: string;
  password?: string;
  confirmPassword?: string;
  accountType?: string;
  form?: string;
  fullName?: string;
};

export async function loader({ request }: Route.LoaderArgs) {
  // If user is already logged in, redirect to appropriate page
  const session = await getUserSession(request);
  if (session.has("jwt")) {
    const accountType = session.get("accountType");
    return redirect(accountType === "business" ? "/business" : "/client");
  }
  return null;
}

export async function action({ request }: Route.ActionArgs) {
  const formData = await request.formData();
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const accountType = formData.get("accountType") as "client" | "business";
  const remember = formData.get("remember") === "on";

  // Validate form
  const errors: FormErrors = {};
  if (!email) errors.email = "Email is required";
  if (!password) errors.password = "Password is required";
  if (!accountType) errors.accountType = "Account type is required";

  if (Object.keys(errors).length > 0) {
    return { errors, fields: { email, remember } };
  }

  try {
    // Login with Strapi
    const { user, jwt } = await loginUser(email, password);

    // Verify account type matches
    if (user.accountType !== accountType) {
      return {
        errors: {
          accountType: `This account is not registered as a ${accountType}`,
        },
        fields: { email, remember },
      };
    }

    // Create session and redirect
    return createUserSession({
      request,
      jwt,
      userId: user.id,
      accountType: user.accountType,
      remember,
      redirectTo: accountType === "business" ? "/business" : "/client",
    });
  } catch (error) {
    console.error("Login error:", error);
    return {
      errors: {
        form: "Invalid email or password",
      },
      fields: { email, remember },
    };
  }
}

export default function Login() {
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [accountType, setAccountType] = useState<"client" | "business">("client");

  return (
    <div className="max-w-md mx-auto mt-8 p-6 border rounded-lg shadow-sm">
      <h1 className="text-2xl font-bold mb-6 text-center">Login</h1>

      {actionData?.errors?.form && (
        <div className="p-3 mb-4 text-sm text-red-800 bg-red-100 rounded-lg">
          {actionData.errors.form}
        </div>
      )}

      <Form method="post" className="space-y-4">
        <div>
          <label htmlFor="email" className="block mb-1">Email</label>
          <input
            type="email"
            id="email"
            name="email"
            className="w-full p-2 border rounded"
            placeholder="Enter your email"
            defaultValue={actionData?.fields?.email || ""}
            aria-invalid={Boolean(actionData?.errors?.email)}
            aria-errormessage={actionData?.errors?.email ? "email-error" : undefined}
          />
          {actionData?.errors?.email && (
            <p className="text-red-600 text-sm mt-1" id="email-error">
              {actionData.errors.email}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block mb-1">Password</label>
          <input
            type="password"
            id="password"
            name="password"
            className="w-full p-2 border rounded"
            placeholder="Enter your password"
            aria-invalid={Boolean(actionData?.errors?.password)}
            aria-errormessage={actionData?.errors?.password ? "password-error" : undefined}
          />
          {actionData?.errors?.password && (
            <p className="text-red-600 text-sm mt-1" id="password-error">
              {actionData.errors.password}
            </p>
          )}
        </div>

        <div>
          <label className="block mb-1">Account Type</label>
          <div className="flex gap-4">
            <div className="flex items-center">
              <input
                type="radio"
                id="client"
                name="accountType"
                value="client"
                className="mr-2"
                checked={accountType === "client"}
                onChange={() => setAccountType("client")}
              />
              <label htmlFor="client">Client</label>
            </div>
            <div className="flex items-center">
              <input
                type="radio"
                id="business"
                name="accountType"
                value="business"
                className="mr-2"
                checked={accountType === "business"}
                onChange={() => setAccountType("business")}
              />
              <label htmlFor="business">Business</label>
            </div>
          </div>
          {actionData?.errors?.accountType && (
            <p className="text-red-600 text-sm mt-1">
              {actionData.errors.accountType}
            </p>
          )}
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="remember"
              name="remember"
              className="mr-2"
              defaultChecked={actionData?.fields?.remember}
            />
            <label htmlFor="remember">Remember me</label>
          </div>
          <Link to="/forgot-password" className="text-blue-600 hover:underline">
            Forgot password?
          </Link>
        </div>

        <div className="pt-2">
          <button
            type="submit"
            className="w-full py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Logging in..." : `Login as ${accountType === "business" ? "Business" : "Client"}`}
          </button>
        </div>

        <div className="text-center mt-4">
          Don't have an account?{" "}
          <Link to="/register" className="text-blue-600 hover:underline">
            Register
          </Link>
        </div>
      </Form>
    </div>
  );
}


